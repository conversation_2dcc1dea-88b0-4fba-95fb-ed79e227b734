#ifndef __TCH34725_H
#define __TCH34725_H

#include "delay.h"
#include <sys.h>	  

typedef struct{
	unsigned short  c;      //[0-65536]
	unsigned short  r;
	unsigned short  g;
	unsigned short  b;
}COLOR_RGBC;//RGBC

typedef struct{
	unsigned short h;       //[0,360]
	unsigned char  s;       //[0,100]
	unsigned char  l;       //[0,100]
}COLOR_HSL;//HSL



void TCS34725_I2C_Init(void);
void TCS34725_I2C_Start(void);
void TCS34725_I2C_Stop(void);
u8 TCS34725_I2C_Wait_ACK(void);
void TCS34725_I2C_ACK(void);
void TCS34725_I2C_NACK(void);
void TCS34725_I2C_Send_Byte(u8 byte);
u8 TCS34725_I2C_Read_Byte(u8 ack);
void TCS34725_I2C_Write(u8 slaveAddress, u8* dataBuffer,u8 bytesNumber, u8 stopBit);
void TCS34725_I2C_Read(u8 slaveAddress, u8* dataBuffer, u8 bytesNumber, u8 stopBit);
void TCS34725_Write(u8 subAddr, u8* dataBuffer, u8 bytesNumber);
void TCS34725_Read(u8 subAddr, u8* dataBuffer, u8 bytesNumber);
void TCS34725_SetIntegrationTime(u8 time);
void TCS34725_SetGain(u8 gain);
void TCS34725_Enable(void);
void TCS34725_Disable(void);
u8 TCS34725_Init(void);
u16 TCS34725_GetChannelData(u8 reg);
u8 TCS34725_GetRawData(COLOR_RGBC *rgbc);
void RGBtoHSL(COLOR_RGBC *Rgb, COLOR_HSL *Hsl);



////////////////////////////////////////////////////////////////////////////////// 	 

#endif
