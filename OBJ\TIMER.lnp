--cpu=Cortex-M4.fp.sp
"..\obj\main.o"
"..\obj\stm32f4xx_it.o"
"..\obj\system_stm32f4xx.o"
"..\obj\stm32f4xx_adc.o"
"..\obj\led.o"
"..\obj\timer.o"
"..\obj\control.o"
"..\obj\encoder.o"
"..\obj\motor.o"
"..\obj\oled.o"
"..\obj\pwm.o"
"..\obj\serial.o"
"..\obj\pid.o"
"..\obj\menu.o"
"..\obj\key.o"
"..\obj\myiic.o"
"..\obj\mpu6050.o"
"..\obj\inv_mpu.o"
"..\obj\inv_mpu_dmp_motion_driver.o"
"..\obj\tcs34725.o"
"..\obj\tcrt5000.o"
"..\obj\wdd35d4.o"
"..\obj\delay.o"
"..\obj\sys.o"
"..\obj\usart.o"
"..\obj\startup_stm32f40_41xxx.o"
"..\obj\misc.o"
"..\obj\stm32f4xx_gpio.o"
"..\obj\stm32f4xx_rcc.o"
"..\obj\stm32f4xx_syscfg.o"
"..\obj\stm32f4xx_tim.o"
"..\obj\stm32f4xx_usart.o"
--library_type=microlib --strict --scatter "..\OBJ\TIMER.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\OBJ\TIMER.map" -o ..\OBJ\TIMER.axf