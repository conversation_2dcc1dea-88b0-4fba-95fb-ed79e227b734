Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to pwm.o(.text) for TIM1_PWM_Init
    main.o(.text) refers to control.o(.text) for Control_Init
    main.o(.text) refers to encoder.o(.text) for Encoder_Init_TIM4
    main.o(.text) refers to serial.o(.text) for Serial_Init
    main.o(.text) refers to oled.o(.text) for OLED_Init
    main.o(.text) refers to pid.o(.text) for PID_init
    main.o(.text) refers to timer.o(.text) for TIM3_Int_Init
    main.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_SetBits
    main.o(.text) refers to main.o(.data) for num
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    timer.o(.text) refers to misc.o(.text) for NVIC_Init
    timer.o(.text) refers to motor.o(.text) for MotorB_Set
    timer.o(.text) refers to encoder.o(.text) for read_Speed1
    timer.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    timer.o(.text) refers to timer.o(.data) for Timeout
    timer.o(.text) refers to encoder.o(.data) for Speed1
    timer.o(.text) refers to timer.o(.bss) for TIM5_ICInitStructure
    control.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    control.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    encoder.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    encoder.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    encoder.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    encoder.o(.text) refers to encoder.o(.data) for Encoder1Count
    motor.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetCompare3
    motor.o(.text) refers to pid.o(.text) for PID_realize
    motor.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    motor.o(.text) refers to dadd.o(.text) for __aeabi_dsub
    motor.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    motor.o(.text) refers to pid.o(.bss) for pidMotor1Speed
    motor.o(.text) refers to encoder.o(.data) for Speed2
    motor.o(.text) refers to motor.o(.data) for turnsign
    motor.o(.text) refers to serial.o(.bss) for hd
    motor.o(.text) refers to timer.o(.data) for hcl0
    motor.o(.text) refers to main.o(.data) for modenow
    oled.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    oled.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    oled.o(.text) refers to delay.o(.text) for delay_us
    oled.o(.text) refers to oled.o(.constdata) for OLED_F8x16
    pwm.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwm.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    pwm.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    serial.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    serial.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    serial.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    serial.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    serial.o(.text) refers to serial.o(.data) for i
    serial.o(.text) refers to serial.o(.bss) for openmv_data
    pid.o(.text) refers to pid.o(.bss) for pidMotor1Speed
    pid.o(.text) refers to pid.o(.data) for last_filt_err
    menu.o(.text) refers to oled.o(.text) for OLED_ShowString
    menu.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to key.o(.data) for key_up
    key.o(.text) refers to main.o(.data) for modenow
    myiic.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    myiic.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    myiic.o(.text) refers to delay.o(.text) for delay_us
    mpu6050.o(.text) refers to myiic.o(.text) for IIC_Start
    mpu6050.o(.text) refers to delay.o(.text) for delay_ms
    mpu6050.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    mpu6050.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    mpu6050.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    mpu6050.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text) refers to mpu6050.o(.text) for MPU_Write_Len
    inv_mpu.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    inv_mpu.o(.text) refers to delay.o(.text) for delay_ms
    inv_mpu.o(.text) refers to inv_mpu.o(.data) for st
    inv_mpu.o(.text) refers to inv_mpu.o(.conststring) for .conststring
    inv_mpu.o(.text) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(.text) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(.text) refers to inv_mpu_dmp_motion_driver.o(.text) for dmp_set_gyro_bias
    inv_mpu.o(.text) refers to myiic.o(.text) for IIC_Init
    inv_mpu.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(.text) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    inv_mpu.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu_dmp_motion_driver.o(.text) refers to inv_mpu.o(.text) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(.text) refers to inv_mpu_dmp_motion_driver.o(.constdata) for dmp_memory
    inv_mpu_dmp_motion_driver.o(.text) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(.text) refers to memseta.o(.text) for __aeabi_memset
    tcs34725.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    tcs34725.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tcs34725.o(.text) refers to delay.o(.text) for delay_us
    tcrt5000.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    tcrt5000.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tcrt5000.o(.text) refers to tcrt5000.o(.bss) for hw5
    wdd35d4.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    wdd35d4.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    wdd35d4.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_DeInit
    wdd35d4.o(.text) refers to delay.o(.text) for delay_ms
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(.text) for TIM3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to serial.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    asin.o(i.__hardfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(.text) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.bss), (100 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing control.o(.rev16_text), (4 bytes).
    Removing control.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing pwm.o(.rev16_text), (4 bytes).
    Removing pwm.o(.revsh_text), (4 bytes).
    Removing serial.o(.rev16_text), (4 bytes).
    Removing serial.o(.revsh_text), (4 bytes).
    Removing menu.o(.rev16_text), (4 bytes).
    Removing menu.o(.revsh_text), (4 bytes).
    Removing menu.o(.text), (664 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing myiic.o(.rev16_text), (4 bytes).
    Removing myiic.o(.revsh_text), (4 bytes).
    Removing myiic.o(.text), (592 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(.text), (900 bytes).
    Removing inv_mpu.o(.rev16_text), (4 bytes).
    Removing inv_mpu.o(.revsh_text), (4 bytes).
    Removing inv_mpu.o(.text), (8892 bytes).
    Removing inv_mpu.o(.constdata), (80 bytes).
    Removing inv_mpu.o(.conststring), (77 bytes).
    Removing inv_mpu.o(.data), (53 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rev16_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.revsh_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text), (3472 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.constdata), (3062 bytes).
    Removing tcs34725.o(.rev16_text), (4 bytes).
    Removing tcs34725.o(.revsh_text), (4 bytes).
    Removing tcs34725.o(.text), (1572 bytes).
    Removing tcs34725.o(.data), (12 bytes).
    Removing tcrt5000.o(.rev16_text), (4 bytes).
    Removing tcrt5000.o(.revsh_text), (4 bytes).
    Removing tcrt5000.o(.text), (124 bytes).
    Removing tcrt5000.o(.bss), (20 bytes).
    Removing tcrt5000.o(.data), (28 bytes).
    Removing wdd35d4.o(.rev16_text), (4 bytes).
    Removing wdd35d4.o(.revsh_text), (4 bytes).
    Removing wdd35d4.o(.text), (216 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.text), (204 bytes).
    Removing usart.o(.bss), (200 bytes).
    Removing usart.o(.data), (6 bytes).
    Removing startup_stm32f40_41xxx.o(HEAP), (512 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing dsqrt.o(.text), (162 bytes).

92 unused section(s) (total 23126 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\IIC\myiic.c                  0x00000000   Number         0  myiic.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MENU\menu.c                  0x00000000   Number         0  menu.o ABSOLUTE
    ..\HARDWARE\MOTOR\motor.c                0x00000000   Number         0  motor.o ABSOLUTE
    ..\HARDWARE\MPU6050\eMPL\inv_mpu.c       0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\HARDWARE\MPU6050\eMPL\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\HARDWARE\MPU6050\mpu6050.c            0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\HARDWARE\OLED\OLED.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\PID\pid.c                    0x00000000   Number         0  pid.o ABSOLUTE
    ..\HARDWARE\PWM\pwm.c                    0x00000000   Number         0  pwm.o ABSOLUTE
    ..\HARDWARE\Serial\Serial.c              0x00000000   Number         0  serial.o ABSOLUTE
    ..\HARDWARE\TCRT5000\TCRT5000.c          0x00000000   Number         0  tcrt5000.o ABSOLUTE
    ..\HARDWARE\TIMER\timer.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\WDD35D4\WDD35D4.c            0x00000000   Number         0  wdd35d4.o ABSOLUTE
    ..\HARDWARE\control\control.c            0x00000000   Number         0  control.o ABSOLUTE
    ..\HARDWARE\encoder\encoder.c            0x00000000   Number         0  encoder.o ABSOLUTE
    ..\HARDWARE\tcs34725\tcs34725.c          0x00000000   Number         0  tcs34725.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\IIC\\myiic.c               0x00000000   Number         0  myiic.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\MENU\\menu.c               0x00000000   Number         0  menu.o ABSOLUTE
    ..\\HARDWARE\\MOTOR\\motor.c             0x00000000   Number         0  motor.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\eMPL\\inv_mpu.c   0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\eMPL\\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\mpu6050.c         0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\HARDWARE\\OLED\\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\PWM\\pwm.c                 0x00000000   Number         0  pwm.o ABSOLUTE
    ..\\HARDWARE\\Serial\\Serial.c           0x00000000   Number         0  serial.o ABSOLUTE
    ..\\HARDWARE\\TCRT5000\\TCRT5000.c       0x00000000   Number         0  tcrt5000.o ABSOLUTE
    ..\\HARDWARE\\TIMER\\timer.c             0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\WDD35D4\\WDD35D4.c         0x00000000   Number         0  wdd35d4.o ABSOLUTE
    ..\\HARDWARE\\control\\control.c         0x00000000   Number         0  control.o ABSOLUTE
    ..\\HARDWARE\\encoder\\encoder.c         0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\HARDWARE\\tcs34725\\tcs34725.c       0x00000000   Number         0  tcs34725.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section        0  main.o(.text)
    .text                                    0x0800024c   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000268   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08000269   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000478   Section        0  led.o(.text)
    .text                                    0x080004e8   Section        0  timer.o(.text)
    .text                                    0x08000714   Section        0  control.o(.text)
    .text                                    0x0800077c   Section        0  encoder.o(.text)
    .text                                    0x080009f8   Section        0  motor.o(.text)
    .text                                    0x08001140   Section        0  oled.o(.text)
    .text                                    0x08001614   Section        0  pwm.o(.text)
    .text                                    0x080017f0   Section        0  serial.o(.text)
    .text                                    0x08001c80   Section        0  pid.o(.text)
    .text                                    0x08001f38   Section        0  key.o(.text)
    .text                                    0x08002038   Section        0  delay.o(.text)
    .text                                    0x0800213c   Section       36  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x0800213c   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08002160   Section        0  misc.o(.text)
    .text                                    0x08002240   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x080024d4   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x08002b30   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x080032cf   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08003331   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08003393   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x080033ff   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x080037d4   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08003c28   Section        0  dadd.o(.text)
    .text                                    0x08003d76   Section        0  f2d.o(.text)
    .text                                    0x08003d9c   Section        0  d2f.o(.text)
    .text                                    0x08003dd4   Section        0  llshl.o(.text)
    .text                                    0x08003df2   Section        0  llsshr.o(.text)
    .text                                    0x08003e16   Section        0  fepilogue.o(.text)
    .text                                    0x08003e16   Section        0  iusefp.o(.text)
    .text                                    0x08003e84   Section        0  depilogue.o(.text)
    .text                                    0x08003f40   Section       36  init.o(.text)
    .text                                    0x08003f64   Section        0  llushr.o(.text)
    i.__scatterload_copy                     0x08003f84   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08003f92   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003f94   Section       14  handlers.o(i.__scatterload_zeroinit)
    .constdata                               0x08003fa2   Section     1520  oled.o(.constdata)
    .data                                    0x20000000   Section       28  main.o(.data)
    .data                                    0x2000001c   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000030   Section       48  timer.o(.data)
    .data                                    0x20000060   Section       16  encoder.o(.data)
    .data                                    0x20000070   Section       32  motor.o(.data)
    .data                                    0x20000090   Section        8  serial.o(.data)
    i                                        0x20000094   Data           4  serial.o(.data)
    .data                                    0x20000098   Section        8  pid.o(.data)
    .data                                    0x200000a0   Section        1  key.o(.data)
    key_up                                   0x200000a0   Data           1  key.o(.data)
    .data                                    0x200000a2   Section        4  delay.o(.data)
    fac_us                                   0x200000a2   Data           1  delay.o(.data)
    fac_ms                                   0x200000a4   Data           2  delay.o(.data)
    .data                                    0x200000a6   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x200000a6   Data          16  stm32f4xx_rcc.o(.data)
    .bss                                     0x200000b8   Section       10  timer.o(.bss)
    .bss                                     0x200000c4   Section       51  serial.o(.bss)
    .bss                                     0x200000f8   Section       96  pid.o(.bss)
    STACK                                    0x20000158   Section     1024  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    main                                     0x080001a1   Thumb Code   154  main.o(.text)
    NMI_Handler                              0x0800024d   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800024f   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000253   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08000257   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800025b   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800025f   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000261   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000263   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000265   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000345   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x0800039d   Thumb Code   174  system_stm32f4xx.o(.text)
    LED_Init                                 0x08000479   Thumb Code   102  led.o(.text)
    TIM3_Int_Init                            0x080004e9   Thumb Code    88  timer.o(.text)
    TIM3_IRQHandler                          0x08000541   Thumb Code    66  timer.o(.text)
    TIM5_CH3_Cap_Init                        0x08000583   Thumb Code   172  timer.o(.text)
    TIM5_IRQHandler                          0x0800062f   Thumb Code   194  timer.o(.text)
    Control_Init                             0x08000715   Thumb Code    96  control.o(.text)
    Encoder_Init_TIM4                        0x0800077d   Thumb Code   200  encoder.o(.text)
    Encoder_Init_TIM2                        0x08000845   Thumb Code   208  encoder.o(.text)
    read_Speed1                              0x08000915   Thumb Code    94  encoder.o(.text)
    read_Speed2                              0x08000973   Thumb Code    92  encoder.o(.text)
    MotorB_Set                               0x080009f9   Thumb Code   194  motor.o(.text)
    motorPidSetSpeed                         0x08000abb   Thumb Code    80  motor.o(.text)
    carturn                                  0x08000b0b   Thumb Code   114  motor.o(.text)
    carwalk                                  0x08000b7d   Thumb Code   110  motor.o(.text)
    carrun                                   0x08000beb   Thumb Code   472  motor.o(.text)
    carrunback                               0x08000dc3   Thumb Code   888  motor.o(.text)
    OLED_I2C_Init                            0x08001141   Thumb Code    68  oled.o(.text)
    OLED_I2C_Start                           0x08001185   Thumb Code    68  oled.o(.text)
    OLED_I2C_Stop                            0x080011c9   Thumb Code    52  oled.o(.text)
    OLED_I2C_SendByte                        0x080011fd   Thumb Code   104  oled.o(.text)
    OLED_WriteCommand                        0x08001265   Thumb Code    32  oled.o(.text)
    OLED_WriteData                           0x08001285   Thumb Code    32  oled.o(.text)
    OLED_SetCursor                           0x080012a5   Thumb Code    34  oled.o(.text)
    OLED_Set_Pos                             0x080012c7   Thumb Code    40  oled.o(.text)
    OLED_Clear                               0x080012ef   Thumb Code    42  oled.o(.text)
    OLED_ShowChar                            0x08001319   Thumb Code   110  oled.o(.text)
    Draw_BMP                                 0x08001387   Thumb Code   118  oled.o(.text)
    OLED_ShowString                          0x080013fd   Thumb Code    40  oled.o(.text)
    OLED_Pow                                 0x08001425   Thumb Code    20  oled.o(.text)
    OLED_ShowNum                             0x08001439   Thumb Code    68  oled.o(.text)
    OLED_ShowSignedNum                       0x0800147d   Thumb Code   102  oled.o(.text)
    OLED_ShowHexNum                          0x080014e3   Thumb Code    84  oled.o(.text)
    OLED_ShowBinNum                          0x08001537   Thumb Code    72  oled.o(.text)
    OLED_Init                                0x0800157f   Thumb Code   150  oled.o(.text)
    TIM1_PWM_Init                            0x08001615   Thumb Code   226  pwm.o(.text)
    TIM9_PWM_Init                            0x080016f7   Thumb Code   232  pwm.o(.text)
    Serial_Init                              0x080017f1   Thumb Code   176  serial.o(.text)
    Serial3_Init                             0x080018a1   Thumb Code   176  serial.o(.text)
    Serial2_Init                             0x08001951   Thumb Code   174  serial.o(.text)
    Serial4_Init                             0x080019ff   Thumb Code   176  serial.o(.text)
    Serial_SendByte                          0x08001aaf   Thumb Code    28  serial.o(.text)
    Serial_SendArray                         0x08001acb   Thumb Code    26  serial.o(.text)
    Serial_SendString                        0x08001ae5   Thumb Code    26  serial.o(.text)
    Serial_Pow                               0x08001aff   Thumb Code    20  serial.o(.text)
    Serial_SendNumber                        0x08001b13   Thumb Code    58  serial.o(.text)
    USART1_IRQHandler                        0x08001b4d   Thumb Code   204  serial.o(.text)
    USART3_IRQHandler                        0x08001c19   Thumb Code    26  serial.o(.text)
    USART2_IRQHandler                        0x08001c33   Thumb Code    36  serial.o(.text)
    UART4_IRQHandler                         0x08001c57   Thumb Code    26  serial.o(.text)
    PID_init                                 0x08001c81   Thumb Code   200  pid.o(.text)
    P_realize                                0x08001d49   Thumb Code    46  pid.o(.text)
    PI_realize                               0x08001d77   Thumb Code    74  pid.o(.text)
    PID_realize                              0x08001dc1   Thumb Code   152  pid.o(.text)
    PD_realize                               0x08001e59   Thumb Code   170  pid.o(.text)
    KEY_Init                                 0x08001f39   Thumb Code    42  key.o(.text)
    KEY_Scan                                 0x08001f63   Thumb Code   200  key.o(.text)
    delay_init                               0x08002039   Thumb Code    52  delay.o(.text)
    delay_us                                 0x0800206d   Thumb Code    72  delay.o(.text)
    delay_xms                                0x080020b5   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x080020fd   Thumb Code    56  delay.o(.text)
    Reset_Handler                            0x0800213d   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08002157   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08002161   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800216b   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x080021d5   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x080021e3   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08002205   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08002241   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x0800234d   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x080023dd   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x080023ef   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08002411   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08002423   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x0800242b   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x0800243d   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08002445   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08002449   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x0800244d   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08002457   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x0800245b   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08002463   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x080024d5   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08002527   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08002535   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08002571   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080025a9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080025bd   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080025c3   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x080025f1   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x080025f7   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08002617   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800261d   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800262b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08002631   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08002645   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x0800264b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08002651   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x0800266d   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08002689   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x0800269d   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x080026a9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x080026bd   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x080026d1   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x080026e7   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x080027c5   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x080027fb   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08002803   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x0800280b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08002811   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x0800282b   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08002847   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x0800285b   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x0800286f   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x08002883   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08002889   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x080028ab   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x080028f9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800291b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x0800293d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x0800295f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08002981   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x080029a3   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x080029c5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x080029e7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08002a09   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08002a2b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08002a4d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08002a6f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08002a91   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08002ab3   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08002adb   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08002afd   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08002b0f   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08002b25   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x08002b31   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x08002c8b   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08002cf3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x08002d05   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x08002d0b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08002d1d   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08002d21   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x08002d25   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x08002d2b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08002d31   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08002d49   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08002d61   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08002d79   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x08002d8b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08002d9d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08002db5   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x08002e27   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08002ec1   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08002f8d   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x08002ffd   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08003011   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x08003067   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x0800306b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x0800306f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x08003073   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x08003077   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08003089   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x080030a3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x080030b5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080030cf   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080030e1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080030fb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x0800310d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08003127   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08003139   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08003153   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08003165   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x0800317f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08003191   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x080031a9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x080031bb   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080031d3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080031e5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080031f7   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08003211   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800322b   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08003245   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x0800325f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08003279   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08003297   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x080032b5   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x0800331f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08003379   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x080033ed   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x08003439   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x080034a7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x080034b9   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08003535   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x0800353b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08003541   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08003547   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x0800354d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x0800356d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x0800357f   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x0800359d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x080035b5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x080035cd   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x080035df   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x080035e3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x080035f5   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x080035fb   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x0800361d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08003623   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x0800362d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x0800363f   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x08003657   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08003663   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08003675   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x0800368d   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x080036cb   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x080036e7   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800371d   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x0800373d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x0800374f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08003761   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08003773   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x080037b5   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x080037cd   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x080037d5   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x080038a3   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x0800396f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08003987   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x080039a7   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x080039b3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x080039cb   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x080039db   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x080039f1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08003a09   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08003a11   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08003a1b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08003a2d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08003a45   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08003a57   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08003a69   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08003a81   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08003a8b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08003aa3   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08003ab3   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08003acb   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x08003ae3   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x08003af5   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08003b0d   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08003b1f   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x08003b69   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08003b83   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08003b95   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08003c0b   Thumb Code    30  stm32f4xx_usart.o(.text)
    __aeabi_dadd                             0x08003c29   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08003d6b   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08003d71   Thumb Code     6  dadd.o(.text)
    __aeabi_f2d                              0x08003d77   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08003d9d   Thumb Code    56  d2f.o(.text)
    __aeabi_llsl                             0x08003dd5   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08003dd5   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08003df3   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08003df3   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08003e17   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08003e17   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08003e29   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08003e85   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08003ea3   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x08003f41   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08003f41   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x08003f65   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08003f65   Thumb Code     0  llushr.o(.text)
    __scatterload_copy                       0x08003f85   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08003f93   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003f95   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    OLED_F8x16                               0x08003fa2   Data        1520  oled.o(.constdata)
    Region$$Table$$Base                      0x08004594   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080045b4   Number         0  anon$$obj.o(Region$$Table)
    pitch                                    0x20000000   Data           4  main.o(.data)
    roll                                     0x20000004   Data           4  main.o(.data)
    yaw                                      0x20000008   Data           4  main.o(.data)
    modenow                                  0x2000000c   Data           4  main.o(.data)
    key                                      0x20000010   Data           4  main.o(.data)
    num                                      0x20000014   Data           4  main.o(.data)
    distance                                 0x20000018   Data           4  main.o(.data)
    SystemCoreClock                          0x2000001c   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000020   Data          16  system_stm32f4xx.o(.data)
    Timeout                                  0x20000030   Data           4  timer.o(.data)
    lastTime                                 0x20000034   Data           4  timer.o(.data)
    hcl0                                     0x20000038   Data           4  timer.o(.data)
    hcl1                                     0x2000003c   Data           4  timer.o(.data)
    hcl2                                     0x20000040   Data           4  timer.o(.data)
    hcl3                                     0x20000044   Data           4  timer.o(.data)
    hcl4                                     0x20000048   Data           4  timer.o(.data)
    hcl5                                     0x2000004c   Data           4  timer.o(.data)
    hcl6                                     0x20000050   Data           4  timer.o(.data)
    n0                                       0x20000054   Data           4  timer.o(.data)
    TIM5CH3_CAPTURE_STA                      0x20000058   Data           1  timer.o(.data)
    TIM5CH3_CAPTURE_VAL                      0x2000005c   Data           4  timer.o(.data)
    Speed1                                   0x20000060   Data           4  encoder.o(.data)
    Speed2                                   0x20000064   Data           4  encoder.o(.data)
    Encoder1Count                            0x20000068   Data           4  encoder.o(.data)
    Encoder2Count                            0x2000006c   Data           4  encoder.o(.data)
    turntime                                 0x20000070   Data           4  motor.o(.data)
    turnsign                                 0x20000074   Data           4  motor.o(.data)
    turnspeed                                0x20000078   Data           4  motor.o(.data)
    walktime                                 0x2000007c   Data           4  motor.o(.data)
    walksign                                 0x20000080   Data           4  motor.o(.data)
    walkspeed                                0x20000084   Data           4  motor.o(.data)
    Speedleft                                0x20000088   Data           4  motor.o(.data)
    Speedright                               0x2000008c   Data           4  motor.o(.data)
    thr                                      0x20000090   Data           4  serial.o(.data)
    filt_err                                 0x20000098   Data           4  pid.o(.data)
    last_filt_err                            0x2000009c   Data           4  pid.o(.data)
    TIM5_ICInitStructure                     0x200000b8   Data          10  timer.o(.bss)
    hd                                       0x200000c4   Data          36  serial.o(.bss)
    openmv_data                              0x200000e8   Data          15  serial.o(.bss)
    pidMotor1Speed                           0x200000f8   Data          32  pid.o(.bss)
    pidMotor2Speed                           0x20000118   Data          32  pid.o(.bss)
    pidTurnSpeed                             0x20000138   Data          32  pid.o(.bss)
    __initial_sp                             0x20000558   Data           0  startup_stm32f40_41xxx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000466c, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000045b4, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          802    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000000   Code   RO          955  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1288    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         1291    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1293    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1295    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1296    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1303    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1298    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1300    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         1289    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x000000ac   Code   RO            3    .text               main.o
    0x0800024c   0x0800024c   0x0000001a   Code   RO          176    .text               stm32f4xx_it.o
    0x08000266   0x08000266   0x00000002   PAD
    0x08000268   0x08000268   0x00000210   Code   RO          225    .text               system_stm32f4xx.o
    0x08000478   0x08000478   0x00000070   Code   RO          271    .text               led.o
    0x080004e8   0x080004e8   0x0000022c   Code   RO          291    .text               timer.o
    0x08000714   0x08000714   0x00000068   Code   RO          319    .text               control.o
    0x0800077c   0x0800077c   0x0000027c   Code   RO          339    .text               encoder.o
    0x080009f8   0x080009f8   0x00000748   Code   RO          362    .text               motor.o
    0x08001140   0x08001140   0x000004d4   Code   RO          389    .text               oled.o
    0x08001614   0x08001614   0x000001dc   Code   RO          415    .text               pwm.o
    0x080017f0   0x080017f0   0x00000490   Code   RO          438    .text               serial.o
    0x08001c80   0x08001c80   0x000002b8   Code   RO          471    .text               pid.o
    0x08001f38   0x08001f38   0x00000100   Code   RO          512    .text               key.o
    0x08002038   0x08002038   0x00000104   Code   RO          731    .text               delay.o
    0x0800213c   0x0800213c   0x00000024   Code   RO          803    .text               startup_stm32f40_41xxx.o
    0x08002160   0x08002160   0x000000e0   Code   RO          809    .text               misc.o
    0x08002240   0x08002240   0x00000294   Code   RO          829    .text               stm32f4xx_gpio.o
    0x080024d4   0x080024d4   0x0000065c   Code   RO          849    .text               stm32f4xx_rcc.o
    0x08002b30   0x08002b30   0x00000ca2   Code   RO          891    .text               stm32f4xx_tim.o
    0x080037d2   0x080037d2   0x00000002   PAD
    0x080037d4   0x080037d4   0x00000454   Code   RO          911    .text               stm32f4xx_usart.o
    0x08003c28   0x08003c28   0x0000014e   Code   RO         1225    .text               mf_w.l(dadd.o)
    0x08003d76   0x08003d76   0x00000026   Code   RO         1233    .text               mf_w.l(f2d.o)
    0x08003d9c   0x08003d9c   0x00000038   Code   RO         1235    .text               mf_w.l(d2f.o)
    0x08003dd4   0x08003dd4   0x0000001e   Code   RO         1308    .text               mc_w.l(llshl.o)
    0x08003df2   0x08003df2   0x00000024   Code   RO         1310    .text               mc_w.l(llsshr.o)
    0x08003e16   0x08003e16   0x00000000   Code   RO         1319    .text               mc_w.l(iusefp.o)
    0x08003e16   0x08003e16   0x0000006e   Code   RO         1320    .text               mf_w.l(fepilogue.o)
    0x08003e84   0x08003e84   0x000000ba   Code   RO         1322    .text               mf_w.l(depilogue.o)
    0x08003f3e   0x08003f3e   0x00000002   PAD
    0x08003f40   0x08003f40   0x00000024   Code   RO         1332    .text               mc_w.l(init.o)
    0x08003f64   0x08003f64   0x00000020   Code   RO         1334    .text               mc_w.l(llushr.o)
    0x08003f84   0x08003f84   0x0000000e   Code   RO         1340    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08003f92   0x08003f92   0x00000002   Code   RO         1341    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003f94   0x08003f94   0x0000000e   Code   RO         1342    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08003fa2   0x08003fa2   0x000005f0   Data   RO          390    .constdata          oled.o
    0x08004592   0x08004592   0x00000002   PAD
    0x08004594   0x08004594   0x00000020   Data   RO         1338    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x0800466c, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080045b4, Size: 0x00000558, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080045b4   0x0000001c   Data   RW            5    .data               main.o
    0x2000001c   0x080045d0   0x00000014   Data   RW          226    .data               system_stm32f4xx.o
    0x20000030   0x080045e4   0x00000030   Data   RW          293    .data               timer.o
    0x20000060   0x08004614   0x00000010   Data   RW          340    .data               encoder.o
    0x20000070   0x08004624   0x00000020   Data   RW          363    .data               motor.o
    0x20000090   0x08004644   0x00000008   Data   RW          440    .data               serial.o
    0x20000098   0x0800464c   0x00000008   Data   RW          473    .data               pid.o
    0x200000a0   0x08004654   0x00000001   Data   RW          513    .data               key.o
    0x200000a1   0x08004655   0x00000001   PAD
    0x200000a2   0x08004656   0x00000004   Data   RW          732    .data               delay.o
    0x200000a6   0x0800465a   0x00000010   Data   RW          850    .data               stm32f4xx_rcc.o
    0x200000b6   0x0800466a   0x00000002   PAD
    0x200000b8        -       0x0000000a   Zero   RW          292    .bss                timer.o
    0x200000c2   0x0800466a   0x00000002   PAD
    0x200000c4        -       0x00000033   Zero   RW          439    .bss                serial.o
    0x200000f7   0x0800466a   0x00000001   PAD
    0x200000f8        -       0x00000060   Zero   RW          472    .bss                pid.o
    0x20000158        -       0x00000400   Zero   RW          800    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       104          8          0          0          0        579   control.o
       260          8          0          4          0       1481   delay.o
       636         42          0         16          0       1774   encoder.o
       256         14          0          1          0        880   key.o
       112         10          0          0          0        583   led.o
       172         18          0         28          0     284259   main.o
       224         20          0          0          0       1817   misc.o
      1864        100          0         32          0       3015   motor.o
      1236         10       1520          0          0       6238   oled.o
       696         54          0          8         96       2391   pid.o
       476         18          0          0          0       1282   pwm.o
      1168         56          0          8         51       4353   serial.o
        36          8        392          0       1024        824   startup_stm32f40_41xxx.o
       660         44          0          0          0       4161   stm32f4xx_gpio.o
        26          0          0          0          0       1222   stm32f4xx_it.o
      1628         52          0         16          0      13032   stm32f4xx_rcc.o
      3234         60          0          0          0      23016   stm32f4xx_tim.o
      1108         34          0          0          0       7884   stm32f4xx_usart.o
       528         46          0         20          0       1779   system_stm32f4xx.o
       556         36          0         48         10       2132   timer.o

    ----------------------------------------------------------------------
     14984        <USER>       <GROUP>        184       1184     362702   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          2          3          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       186          0          0          0          0        176   depilogue.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
       914         <USER>          <GROUP>          0          0        920   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       188         16          0          0          0        272   mc_w.l
       724          0          0          0          0        648   mf_w.l

    ----------------------------------------------------------------------
       914         <USER>          <GROUP>          0          0        920   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15898        654       1946        184       1184     360614   Grand Totals
     15898        654       1946        184       1184     360614   ELF Image Totals
     15898        654       1946        184          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17844 (  17.43kB)
    Total RW  Size (RW Data + ZI Data)              1368 (   1.34kB)
    Total ROM Size (Code + RO Data + RW Data)      18028 (  17.61kB)

==============================================================================

