..\obj\menu.o: ..\HARDWARE\MENU\menu.c
..\obj\menu.o: ..\HARDWARE\MENU\menu.h
..\obj\menu.o: ..\SYSTEM\delay\delay.h
..\obj\menu.o: ..\SYSTEM\sys\sys.h
..\obj\menu.o: ..\USER\stm32f4xx.h
..\obj\menu.o: ..\CORE\core_cm4.h
..\obj\menu.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\menu.o: ..\CORE\core_cmInstr.h
..\obj\menu.o: ..\CORE\core_cmFunc.h
..\obj\menu.o: ..\CORE\core_cm4_simd.h
..\obj\menu.o: ..\USER\system_stm32f4xx.h
..\obj\menu.o: ..\USER\stm32f4xx_conf.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\menu.o: ..\USER\stm32f4xx.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\menu.o: ..\FWLIB\inc\misc.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\menu.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\menu.o: ..\HARDWARE\OLED\oled.h
..\obj\menu.o: ..\HARDWARE\LED\led.h
