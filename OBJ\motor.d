..\obj\motor.o: ..\HARDWARE\MOTOR\motor.c
..\obj\motor.o: ..\HARDWARE\MOTOR\motor.h
..\obj\motor.o: ..\SYSTEM\sys\sys.h
..\obj\motor.o: ..\USER\stm32f4xx.h
..\obj\motor.o: ..\CORE\core_cm4.h
..\obj\motor.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\motor.o: ..\CORE\core_cmInstr.h
..\obj\motor.o: ..\CORE\core_cmFunc.h
..\obj\motor.o: ..\CORE\core_cm4_simd.h
..\obj\motor.o: ..\USER\system_stm32f4xx.h
..\obj\motor.o: ..\USER\stm32f4xx_conf.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\motor.o: ..\USER\stm32f4xx.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\motor.o: ..\FWLIB\inc\misc.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\motor.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\motor.o: ..\HARDWARE\LED\led.h
..\obj\motor.o: ..\HARDWARE\PID\pid.h
..\obj\motor.o: ..\HARDWARE\Serial\Serial.h
..\obj\motor.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\motor.o: ..\SYSTEM\delay\delay.h
..\obj\motor.o: ..\HARDWARE\tcs34725\tcs34725.h
