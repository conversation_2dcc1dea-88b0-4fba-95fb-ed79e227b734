{"name": "TIMER", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "USER", "files": [{"path": "main.c"}, {"path": "stm32f4xx_it.c"}, {"path": "system_stm32f4xx.c"}, {"path": "../FWLIB/src/stm32f4xx_adc.c"}], "folders": []}, {"name": "HARDWARE", "files": [{"path": "../HARDWARE/LED/led.c"}, {"path": "../HARDWARE/TIMER/timer.c"}, {"path": "../HARDWARE/control/control.c"}, {"path": "../HARDWARE/encoder/encoder.c"}, {"path": "../HARDWARE/MOTOR/motor.c"}, {"path": "../HARDWARE/OLED/OLED.c"}, {"path": "../HARDWARE/PWM/pwm.c"}, {"path": "../HARDWARE/Serial/Serial.c"}, {"path": "../HARDWARE/PID/pid.c"}, {"path": "../HARDWARE/MENU/menu.c"}, {"path": "../HARDWARE/KEY/key.c"}, {"path": "../HARDWARE/IIC/myiic.c"}, {"path": "../HARDWARE/MPU6050/mpu6050.c"}, {"path": "../HARDWARE/MPU6050/eMPL/inv_mpu.c"}, {"path": "../HARDWARE/MPU6050/eMPL/inv_mpu_dmp_motion_driver.c"}, {"path": "../HARDWARE/HC_SR04/hc_sr04.c"}, {"path": "../HARDWARE/tcs34725/tcs34725.c"}, {"path": "../HARDWARE/TCRT5000/TCRT5000.c"}, {"path": "../HARDWARE/WDD35D4/WDD35D4.c"}], "folders": []}, {"name": "SYSTEM", "files": [{"path": "../SYSTEM/delay/delay.c"}, {"path": "../SYSTEM/sys/sys.c"}, {"path": "../SYSTEM/usart/usart.c"}], "folders": []}, {"name": "CORE", "files": [{"path": "../CORE/startup_stm32f40_41xxx.s"}], "folders": []}, {"name": "FWLIB", "files": [{"path": "../FWLIB/src/misc.c"}, {"path": "../FWLIB/src/stm32f4xx_gpio.c"}, {"path": "../FWLIB/src/stm32f4xx_rcc.c"}, {"path": "../FWLIB/src/stm32f4xx_syscfg.c"}, {"path": "../FWLIB/src/stm32f4xx_tim.c"}, {"path": "../FWLIB/src/stm32f4xx_usart.c"}], "folders": []}, {"name": "README", "files": [{"path": "../readme.txt"}], "folders": []}]}, "outDir": "build", "deviceName": "STM32F407ZG", "packDir": ".pack/Keil/STM32F4xx_DFP.1.0.8", "miscInfo": {"uid": "4b06bb33c46de6d8a2b75a0a8a018c75"}, "targets": {"TIMER": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x20000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x10000000", "size": "0x10000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x100000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../CORE", "../SYSTEM/delay", "../SYSTEM/sys", ".", "../HARDWARE/LED", "../HARDWARE/TIMER", "../FWLIB/inc", "../HARDWARE/control", "../HARDWARE/encoder", "../HARDWARE/MOTOR", "../HARDWARE/OLED", "../HARDWARE/Serial", "../HARDWARE/PWM", "../HARDWARE/PID", "../HARDWARE/MENU", "../HARDWARE/KEY", "../HARDWARE/IIC", "../HARDWARE/MPU6050", "../HARDWARE/MPU6050/eMPL", "../SYSTEM/usart", "../HARDWARE/HC_SR04", "../HARDWARE/tcs34725", "../HARDWARE/TCRT5000", "../HARDWARE/WDD35D4", ".cmsis/include", "RTE/_TIMER"], "libList": [], "defineList": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}