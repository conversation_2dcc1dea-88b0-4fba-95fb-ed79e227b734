The 'Blinky' project is a simple program for
ST 'STM32F407' microcontroller using Keil 'MCBSTM32F400' Evaluation Board.
Compliant to Cortex Microcontroller Software Interface Standard (CMSIS v2.0).

Example functionality:                                                   
 - Clock Settings:
   - XTAL    =           25.00 MHz
   - SYSCLK  =          168.00 MHz

 - Sys Timer is used in interrupt mode
 - LEDs are blinking with speed depending on SysTick timer interrupt period
 

The Blinky program is available in different targets:

  STM32F407 RAM:    runs from Internal RAM located on chip
                    (may be used for target debugging)

  STM32F407 Flash:  runs from Internal Flash located on chip
                    (used for production or target debugging)

  STM32F407 OPT:    STM32F407 with Flash Options Bytes
                    (used for programming)

  STM32F407 OTP:    STM32F407 with Flash One-Time Programmable Bytes
                    (used for programming)

  STM32F407 Flash + ULP:  runs from Internal Flash located on chip
                    configured for ULink-Pro and ETM based Instruction Trace
                    (used for production or target debugging)

ULINKpro notes
--------------
STM32F2xx_TP.ini enables synchronous 4bit Trace Interface
ETM Trace pins:  TRACECK         PE2
                 TRACED0..3      PE3..PE6    (4 bit trace data)
                 do not use these pins in your application!

Instruction trace is very time and resource consuming
therefore if you want to use instruction trace use
nothing else (no LA, no exceptions, no ITM, no ...).

Jumper Configuration:
  Set jumpers J10, J11, J12 (close to ETM connector) to position 'Trace'.
