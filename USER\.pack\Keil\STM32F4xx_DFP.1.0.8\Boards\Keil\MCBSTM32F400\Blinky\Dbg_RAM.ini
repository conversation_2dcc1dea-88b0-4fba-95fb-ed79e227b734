/******************************************************************************/
/* Dbg_RAM.INI: RAM Initialization File                                       */
/******************************************************************************/
// <<< Use Configuration Wizard in Context Menu >>>                           // 
/******************************************************************************/
/* This file is part of the uVision/ARM development tools.                    */
/* Copyright (c) 2004-2013 KEIL - An ARM Company. All rights reserved.        */
/* This software may only be used under the terms of a valid, current,        */
/* end user licence from KEIL for a compatible version of KEIL software       */
/* development tools. Nothing else gives you the right to use this software.  */
/******************************************************************************/

FUNC void Setup (void) {
  SP = _RDWORD(0x20000000);          // Setup Stack Pointer
  PC = _RDWORD(0x20000004);          // Setup Program Counter
  _WDWORD(0xE000ED08, 0x20000000);   // Setup Vector Table Offset Register
}

load %L incremental

Setup();                             // Setup for Running

g, main
