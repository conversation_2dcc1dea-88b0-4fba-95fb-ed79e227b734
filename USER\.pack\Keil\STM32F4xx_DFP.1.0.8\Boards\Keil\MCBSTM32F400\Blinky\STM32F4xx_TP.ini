/******************************************************************************/
/* STM32_TP.ini: STM32 Debugger Initialization File                           */
/******************************************************************************/
// <<< Use Configuration Wizard in Context Menu >>>                           // 
/******************************************************************************/
/* This file is part of the uVision/ARM development tools.                    */
/* Copyright (c) 2005-2011 Keil Software. All rights reserved.                */
/* This software may only be used under the terms of a valid, current,        */
/* end user licence from KEIL for a compatible version of KEIL software       */
/* development tools. Nothing else gives you the right to use this software.  */
/******************************************************************************/


FUNC void DebugSetup (void) {
  _WDWORD(0x40023830, _RDWORD(0x40023830) | 0x00000010); // RCC_AHB1ENR:   IO port E clock enable
  _WDWORD(0x40021000, 0x00002AA0);                       // GPIOE_MODER:   PE2..PE6 = Alternate function mode
  _WDWORD(0x40021008, 0x00003FF0);                       // GPIOE_OSPEEDR: PE2..PE6 = 100 MHz speed
  _WDWORD(0x4002100C, 0x00000000);                       // GPIOE_PUPDR:   PE2..PE6 = No Pull-up/Pull-down
  _WDWORD(0x40021020, 0x00000000);                       // GPIOE_AFRL:    PE2..PE6 = AF0

// <h> Debug MCU Configuration
//   <o1.0>    DBG_SLEEP     <i> Debug Sleep Mode
//   <o1.1>    DBG_STOP      <i> Debug Stop Mode
//   <o1.2>    DBG_STANDBY   <i> Debug Standby Mode
//   <o1.5>    TRACE_IOEN    <i> Trace I/O Enable 
//   <o1.6..7> TRACE_MODE    <i> Trace Mode
//             <0=> Asynchronous
//             <1=> Synchronous: TRACEDATA Size 1
//             <2=> Synchronous: TRACEDATA Size 2
//             <3=> Synchronous: TRACEDATA Size 4
// </h>
  _WDWORD(0xE0042004, 0x000000E7);  // DBGMCU_CR

// <h> Debug MCU APB1 Freeze
//   <o1.0>    DBG_TIM2_STOP  <i> Timer 2 Stopped when Core is halted
//   <o1.1>    DBG_TIM3_STOP  <i> Timer 3 Stopped when Core is halted
//   <o1.2>    DBG_TIM4_STOP  <i> Timer 4 Stopped when Core is halted
//   <o1.3>    DBG_TIM5_STOP  <i> Timer 5 Stopped when Core is halted
//   <o1.4>    DBG_TIM6_STOP  <i> Timer 6 Stopped when Core is halted
//   <o1.5>    DBG_TIM7_STOP  <i> Timer 7 Stopped when Core is halted
//   <o1.6>    DBG_TIM12_STOP <i> Timer 12 Stopped when Core is halted
//   <o1.7>    DBG_TIM13_STOP <i> Timer 13 Stopped when Core is halted
//   <o1.8>    DBG_TIM14_STOP <i> Timer 14 Stopped when Core is halted
//   <o1.10>   DBG_RTC_STOP   <i> RTC Stopped when Core is halted
//   <o1.11>   DBG_WWDG_STOP  <i> Window Watchdog Stopped when Core is halted
//   <o1.12>   DBG_IWDG_STOP  <i> Independent Watchdog Stopped when Core is halted
//   <o1.21>   DBG_I2C1_SMBUS_TIMEOUT <i> I2C1 SMBUS Timeout Mode Stopped when Core is halted
//   <o1.22>   DBG_I2C2_SMBUS_TIMEOUT <i> I2C2 SMBUS Timeout Mode Stopped when Core is halted
//   <o1.23>   DBG_I2C3_SMBUS_TIMEOUT <i> I2C3 SMBUS Timeout Mode Stopped when Core is halted
//   <o1.25>   DBG_CAN1_STOP  <i> CAN1 Stopped when Core is halted
//   <o1.26>   DBG_CAN2_STOP  <i> CAN2 Stopped when Core is halted
// </h>
  _WDWORD(0xE0042008, 0x00000000);  // DBGMCU_APB1_FZ

// <h> Debug MCU APB2 Freeze
//   <o1.0>    DBG_TIM1_STOP  <i> Timer 1 Stopped when Core is halted
//   <o1.1>    DBG_TIM8_STOP  <i> Timer 8 Stopped when Core is halted
//   <o1.16>   DBG_TIM9_STOP  <i> Timer 9 Stopped when Core is halted
//   <o1.17>   DBG_TIM10_STOP <i> Timer 10 Stopped when Core is halted
//   <o1.18>   DBG_TIM11_STOP <i> Timer 11 Stopped when Core is halted
// </h>
  _WDWORD(0xE004200C, 0x00000000);  // DBGMCU_APB2_FZ

}

// Executed after reset via uVision's 'Reset'-button
FUNC void OnResetExec (void) {
  DebugSetup();
}

DebugSetup();                       // Debugger Setup
