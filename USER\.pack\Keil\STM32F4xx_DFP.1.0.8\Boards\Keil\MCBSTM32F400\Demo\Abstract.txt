The 'Demo' project is a demo program for the STM32F207Z microcontroller
using Keil 'MCBSTM32F200' Evaluation Board, compliant to Cortex
Microcontroller Software Interface Standard (CMSIS v2.0).

Example functionality:                                                   
 - Clock Settings:
   - XTAL    =           25.00 MHz
   - SYSCLK  =          120.00 MHz

 - Sys Timer is used in interrupt mode
 - Graphical LCD display shows three possible example modes
  
   Display shows:
    a) - 12-bit AD converter value bargraph depending on potentiometer position
       - State of buttons
       - Joystick directions
       - Touchscreen dependent images
    
    b) - Accelerometer output
       - Gyroscope output

    c) - Digital camera output stream


Press or hold the WAKEUP key to change between example modes.
 

The Demo program is available for two targets:

  STM32F207 Flash:    runs from Internal Flash located on chip
                      (used for production or target debugging)

  STM32F207 MDK-Lite: same as 'STM32F207 Flash' but the bitmap-button
                      is exchanged with a text-button to meet the code 
                      size limit of MDK-Lite (32K)

Jumper settings:
  - J17 must be shorted to enable digital camera