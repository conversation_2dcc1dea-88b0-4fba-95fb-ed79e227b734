
/*
 * Auto generated Run-Time-Environment Component Configuration File
 *      *** Do not modify ! ***
 *
 * Project: 'File_Demo' 
 * Target:  'STM32F407 Flash' 
 */

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

#define RTE_DEVICE_STARTUP_STM32F4xx    /* Device Startup for STM32F4 */
#define RTE_Drivers_MCI0                /* Driver MCI0 */
#define RTE_Drivers_NAND_MemoryBus      /* Driver NAND Flash on Memory Bus */
#define RTE_Drivers_NOR_M29W640FB       /* Driver M29W640FB NOR Flash */
#define RTE_FileSystem_Core             /* File System Core */
          #define RTE_FileSystem_LFN              /* File System with Long Filename support */
#define RTE_FileSystem_Drive_MC_0       /* File System Memory Card Drive 0 */
#define RTE_FileSystem_Drive_NAND_0     /* File System NAND Flash Drive 0 */
#define RTE_FileSystem_Drive_NOR_0      /* File System NOR Flash Drive 0 */

#endif /* RTE_COMPONENTS_H */
