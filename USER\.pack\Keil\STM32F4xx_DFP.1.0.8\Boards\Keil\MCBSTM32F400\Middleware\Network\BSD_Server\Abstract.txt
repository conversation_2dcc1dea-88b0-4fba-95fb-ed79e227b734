This program is a BSD_Server example. It is used to serve LED control
requests received from the client.

In order to run this example, you need to set the network parameters
to match your local area network.

To test this example, run windows application "LEDSwitch.exe" on your PC.
Enter the IP address, server port and select the transfer protocol TCP or UDP.
When you change the value of P2 in windows application, LED diodes on
evaluation board should light accordingly.

The same example can be tested also with BSD_Client running on another
evaluation board. 

The BSD_Server example is available for one target:

STM32F407 Flash:
    Standalone application for MCBSTM32F400 Board.
    Program code is loaded into on-chip flash.
