<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>STM32F407 Flash</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F407IG</Device>
          <Vendor>STMicroelectronics</Vendor>
          <Cpu>IROM(0x08000000,0x100000) IRAM(0x20000000,0x20000) IRAM2(0x10000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(168000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN3 -FF0STM32F4xx_1024 -********** -********* -FF1STM32F4xx_OPT -FS11FFFC000 -FL110 -FF2STM32F4xx_OTP -FS21FFF7800 -FL2210 -FP0($$Device:STM32F407IG$Flash\STM32F4xx_1024.FLM))</FlashDriverDll>
          <DeviceId>6104</DeviceId>
          <RegisterFile>$$Device:STM32F407IG$Device\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F407IG$SVD\STM32F40x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Output\</OutputDirectory>
          <OutputName>BSD_Server</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Output\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>-REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments>-MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreTracepoints>1</RestoreTracepoints>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>1</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\UL2CM3.DLL</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls>--diag_suppress=C4017</MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Source</GroupName>
          <Files>
            <File>
              <FileName>BSD_Server.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\BSD_Server.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Documentation</GroupName>
          <Files>
            <File>
              <FileName>Abstract.txt</FileName>
              <FileType>5</FileType>
              <FilePath>.\Abstract.txt</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::Board Support</GroupName>
          <Files>
            <File>
              <FileName>GLCD_16bitIF_STM32F4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Boards\Keil\MCBSTM32F400\Common\GLCD_16bitIF_STM32F4xx.c</FilePath>
            </File>
            <File>
              <FileName>LED.c</FileName>
              <FileType>1</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Boards\Keil\MCBSTM32F400\Common\LED.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
          <Files>
            <File>
              <FileName>RTX_Conf_CM.c</FileName>
              <FileType>1</FileType>
              <FilePath>RTE\CMSIS\RTX_Conf_CM.c</FilePath>
            </File>
            <File>
              <FileName>RTX_CM4.lib</FileName>
              <FileType>4</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\ARM\CMSIS\3.20.4\CMSIS_RTX\Lib\ARM\RTX_CM4.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::Device</GroupName>
          <Files>
            <File>
              <FileName>RTE_Device.h</FileName>
              <FileType>5</FileType>
              <FilePath>RTE\Device\STM32F407IG\RTE_Device.h</FilePath>
            </File>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>RTE\Device\STM32F407IG\system_stm32f4xx.c</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f40_41xxx.s</FileName>
              <FileType>2</FileType>
              <FilePath>RTE\Device\STM32F407IG\startup_stm32f40_41xxx.s</FilePath>
            </File>
            <File>
              <FileName>GPIO_STM32F4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\GPIO_STM32F4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::Drivers</GroupName>
          <Files>
            <File>
              <FileName>PHY_ST802RT1.c</FileName>
              <FileType>1</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\Keil\MDK-Middleware\5.1.5\Network\Driver\PHY_ST802RT1.c</FilePath>
            </File>
            <File>
              <FileName>EMAC_STM32F4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\EMAC_STM32F4xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::Network</GroupName>
          <Files>
            <File>
              <FileName>Net_Config.c</FileName>
              <FileType>1</FileType>
              <FilePath>RTE\Network\Net_Config.c</FilePath>
            </File>
            <File>
              <FileName>Net_Config_BSD.h</FileName>
              <FileType>5</FileType>
              <FilePath>RTE\Network\Net_Config_BSD.h</FilePath>
            </File>
            <File>
              <FileName>Net_Config_ETH_0.h</FileName>
              <FileType>5</FileType>
              <FilePath>RTE\Network\Net_Config_ETH_0.h</FilePath>
            </File>
            <File>
              <FileName>Net_Config_TCP.h</FileName>
              <FileType>5</FileType>
              <FilePath>RTE\Network\Net_Config_TCP.h</FilePath>
            </File>
            <File>
              <FileName>Net_Config_UDP.h</FileName>
              <FileType>5</FileType>
              <FilePath>RTE\Network\Net_Config_UDP.h</FilePath>
            </File>
            <File>
              <FileName>Net_CM3_L.lib</FileName>
              <FileType>4</FileType>
              <FilePath>C:\MDK510_ORG\ARM\PACK\Keil\MDK-Middleware\5.1.5\Network\Lib\ARM\Net_CM3_L.lib</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis>
      <api Cclass="CMSIS" Cgroup="RTOS" Cvendor="ARM" exclusive="0">
        <package license="CMSIS\CMSIS END USER LICENCE AGREEMENT.rtf" name="CMSIS" url="http://www.keil.com/pack/" vendor="ARM" version="3.20.0"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </api>
      <api Cclass="Drivers" Cgroup="Ethernet MAC" Cvendor="Keil" exclusive="0">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.0.1"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </api>
      <api Cclass="Drivers" Cgroup="Ethernet PHY" Cvendor="Keil" exclusive="0">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.0.1"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </api>
    </apis>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="3.20.0" condition="CMSIS Core">
        <package license="CMSIS\CMSIS END USER LICENCE AGREEMENT.rtf" name="CMSIS" url="http://www.keil.com/pack/" vendor="ARM" version="3.20.0"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cclass="CMSIS" Cgroup="RTOS" Csub="Keil RTX" Cvendor="ARM" Cversion="4.74.0" condition="CMSIS Core">
        <package name="CMSIS" url="http://www.keil.com/pack/" vendor="ARM" version="3.20.4"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MCBSTM32F400" Cclass="Board Support" Cgroup="MCBSTM32F400" Csub="Graphic LCD" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS Device">
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.4"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MCBSTM32F400" Cclass="Board Support" Cgroup="MCBSTM32F400" Csub="LED" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS GPIO">
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.4"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="CORE" Cvariant="Release" Cvendor="Keil" Cversion="5.0.4" condition="CMSIS Core with RTOS">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.2"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Interface" Csub="ETH" Cvendor="Keil" Cversion="5.0.4" condition="Network Driver ETH" maxInstances="1">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.2"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Socket" Csub="BSD" Cvendor="Keil" Cversion="5.0.4" condition="Network UDP/TCP">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.2"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Socket" Csub="TCP" Cvendor="Keil" Cversion="5.0.4" condition="Network Interface">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.2"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Socket" Csub="UDP" Cvendor="Keil" Cversion="5.0.4" condition="Network Interface">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.2"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cclass="Device" Cgroup="GPIO" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS Device">
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.1"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS Device">
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.7"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cclass="Drivers" Cgroup="Ethernet MAC" Cvendor="Keil" Cversion="1.02.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS RTOS GPIO">
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.4"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
      <component Cclass="Drivers" Cgroup="Ethernet PHY" Csub="ST802RT1" Cvendor="Keil" Cversion="5.01.0" condition="CMSIS Core with RTOS">
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.3"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="source" name="CMSIS_RTX\Templates\RTX_Conf_CM.c">
        <instance index="0">RTE\CMSIS\RTX_Conf_CM.c</instance>
        <component Cclass="CMSIS" Cgroup="RTOS" Csub="Keil RTX" Cvendor="ARM" Cversion="4.74.0" condition="CMSIS Core"/>
        <package name="CMSIS" url="http://www.keil.com/pack/" vendor="ARM" version="3.20.4"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="header" name="RTE_Driver\Config\RTE_Device.h" version="1.3.1">
        <instance index="0">RTE\Device\STM32F407IG\RTE_Device.h</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.1" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" schemaVersion="1.2" url="http://www.keil.com/pack" vendor="Keil" version="1.0.7"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" condition="STM32F40x" name="Device\Source\ARM\startup_stm32f40_41xxx.s">
        <instance index="0">RTE\Device\STM32F407IG\startup_stm32f40_41xxx.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.7"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" condition="STM32F40x" name="Device\Source\ARM\startup_stm32f40xx.s">
        <instance index="0" removed="1">RTE\Device\STM32F407IG\startup_stm32f40xx.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.0.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.4"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\Source\system_stm32f4xx.c">
        <instance index="0">RTE\Device\STM32F407IG\system_stm32f4xx.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Keil" Cversion="1.3.0" Dname="STM32F407IG" condition="STM32F4xx CMSIS Device"/>
        <package name="STM32F4xx_DFP" url="http://www.keil.com/pack" vendor="Keil" version="1.0.7"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Network\Config\Net_Config.c">
        <instance index="0">RTE\Network\Net_Config.c</instance>
        <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="CORE" Cvariant="Release" Cvendor="Keil" Cversion="5.0.4" condition="CMSIS Core with RTOS"/>
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.3"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Network\Config\Net_Config_BSD.h">
        <instance index="0">RTE\Network\Net_Config_BSD.h</instance>
        <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Socket" Csub="BSD" Cvendor="Keil" Cversion="5.0.4" condition="Network UDP/TCP"/>
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.3"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Network\Config\Net_Config_ETH.h">
        <instance index="0">RTE\Network\Net_Config_ETH_0.h</instance>
        <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Interface" Csub="ETH" Cvendor="Keil" Cversion="5.0.4" condition="Network Driver ETH" maxInstances="1"/>
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.3"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Network\Config\Net_Config_TCP.h">
        <instance index="0">RTE\Network\Net_Config_TCP.h</instance>
        <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Socket" Csub="TCP" Cvendor="Keil" Cversion="5.0.4" condition="Network Interface"/>
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.3"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Network\Config\Net_Config_UDP.h">
        <instance index="0">RTE\Network\Net_Config_UDP.h</instance>
        <component Cbundle="MDK-Pro" Cclass="Network" Cgroup="Socket" Csub="UDP" Cvendor="Keil" Cversion="5.0.4" condition="Network Interface"/>
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.1.3"/>
        <targetInfos>
          <targetInfo name="STM32F407 Flash"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Network\Config\Net_Config_ETH.h">
        <instance index="0" removed="1">RTE\Network\Net_config_ETH_0.h</instance>
        <component Cclass="Network" Cgroup="Interface" Csub="ETH" Cvendor="Keil" Cversion="5.0.1" RTE_Components_h="#define RTE_Network_Interface_ETH_%Instance%     /* Network Interface ETH %Instance% */" condition="Network Driver ETH" maxInstances="1"/>
        <package name="MDK-Middleware" url="http://www.keil.com/pack/" vendor="Keil" version="5.0.1"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

</Project>
