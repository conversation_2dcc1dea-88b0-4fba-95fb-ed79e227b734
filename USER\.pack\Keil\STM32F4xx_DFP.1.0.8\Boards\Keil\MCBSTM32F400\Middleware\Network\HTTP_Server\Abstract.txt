This is a HTTP_Server example. It shows you how to use the
Web Server CGI interface as well.

Detailed description is available on:
www.keil.com/pack/doc/MW/Network/html/_compact__web__server__example.html

Use this example to connect an evaluation board to a LAN with DHCP
server (most LANs have this). The example will configure the network
parameters automatically using a DHCP protocol.

If a DHCP server is not available, you may connect an evaluation
board to PC directly over a crosslink network cable. In this case
configure a PC to use a static IP address *********** and disable
a 'Dynamic Host Configuration' in Net_Config.c configuration file.
The default static IP address of this example is then *************

To test this example, open your web browser and enter the
address http://mcbstm32f400/ or http://<boards IP address>

Default user    : admin
Default password: <none>

The HTTP_Demo example is available for one target:

STM32F407 Flash:
    Standalone application for MCBSTM32F400 Board.
    Program code is loaded into on-chip flash.
