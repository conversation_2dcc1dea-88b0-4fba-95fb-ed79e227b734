This is an Mass Storage example that demonstrates Mass Storage Class (MSC)
on USB Device.

Detailed description is available on:
www.keil.com/pack/doc/MW/USB/html/dev_msc_tutorial.html

Board:                  Keil 'MCBSTM32F400'
Microcontroller:        ST   'STM32F407'
Clock Settings:         XTAL       =  25 MHz
                        CPUCLK     = 168 MHz
                        USB FS CLK =  48 MHz
                        USB HS CLK =  60 MHz (from external ULPI)
Storage Media (USBFS):  RAM memory
Storage Media (USBHS):  SD Card

The program is available for target(s):

  - STM32F407 Flash: Downloads to and executes from internal Flash
