
/*
 * Auto generated Run-Time-Environment Component Configuration File
 *      *** Do not modify ! ***
 *
 * Project: 'MassStorage' 
 * Target:  'STM32F407 Flash' 
 */

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

#define RTE_DEVICE_STARTUP_STM32F4xx    /* Device Startup for STM32F4 */
#define RTE_Drivers_MCI0                /* Driver MCI0 */
#define RTE_Drivers_USBD0               /* Driver USBD0 */
#define RTE_Drivers_USBD1               /* Driver USBD1 */
#define RTE_FileSystem_Core             /* File System Core */
          #define RTE_FileSystem_LFN              /* File System with Long Filename support */
#define RTE_FileSystem_Drive_MC_0       /* File System Memory Card Drive 0 */
#define RTE_USB_Core                    /* USB Core */
#define RTE_USB_Device_0                /* USB Device 0 */
#define RTE_USB_Device_1                /* USB Device 1 */
#define RTE_USB_Device_MSC_0            /* USB Device MSC instance 0 */
#define RTE_USB_Device_MSC_1            /* USB Device MSC instance 1 */

#endif /* RTE_COMPONENTS_H */
