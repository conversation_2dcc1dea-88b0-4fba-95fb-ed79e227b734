
/*
 * Auto generated Run-Time-Environment Component Configuration File
 *      *** Do not modify ! ***
 *
 * Project: 'VirtualCOM' 
 * Target:  'STM32F407 Flash' 
 */

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

#define RTE_DEVICE_STARTUP_STM32F4xx    /* Device Startup for STM32F4 */
#define RTE_Drivers_UART1               /* Driver UART1 */
        #define RTE_Drivers_UART2               /* Driver UART2 */
        #define RTE_Drivers_UART3               /* Driver UART3 */
        #define RTE_Drivers_UART4               /* Driver UART4 */
        #define RTE_Drivers_UART5               /* Driver UART5 */
        #define RTE_Drivers_UART6               /* Driver UART6 */
#define RTE_Drivers_USBD0               /* Driver USBD0 */
#define RTE_Drivers_USBD1               /* Driver USBD1 */
#define RTE_USB_Core                    /* USB Core */
#define RTE_USB_Device_0                /* USB Device 0 */
#define RTE_USB_Device_1                /* USB Device 1 */
#define RTE_USB_Device_CDC_0            /* USB Device CDC instance 0 */
#define RTE_USB_Device_CDC_1            /* USB Device CDC instance 1 */

#endif /* RTE_COMPONENTS_H */
