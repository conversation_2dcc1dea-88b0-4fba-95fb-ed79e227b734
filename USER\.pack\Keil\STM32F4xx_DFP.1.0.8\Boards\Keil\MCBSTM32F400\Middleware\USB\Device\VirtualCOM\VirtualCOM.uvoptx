<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_optx.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>STM32F407 Flash</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>168000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\Output\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <Books>
        <Book>
          <Number>0</Number>
          <Title>Getting Started (STM32F4-Discovery)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\**********.pdf</Path>
        </Book>
        <Book>
          <Number>1</Number>
          <Title>User Manual (STM32F4-Discovery)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\**********.pdf</Path>
        </Book>
        <Book>
          <Number>2</Number>
          <Title>User Manual (MCBSTM32F400)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\mcbstm32f200.chm</Path>
        </Book>
        <Book>
          <Number>3</Number>
          <Title>Schematics (MCBSTM32F400)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\mcbstm32f400-schematics.pdf</Path>
        </Book>
        <Book>
          <Number>4</Number>
          <Title>Bill of Materials (STM32F4-Discovery)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\stm32f4discovery_bom.zip</Path>
        </Book>
        <Book>
          <Number>5</Number>
          <Title>Gerber Files (STM32F4-Discovery)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\stm32f4discovery_gerber.zip</Path>
        </Book>
        <Book>
          <Number>6</Number>
          <Title>Schematics (STM32F4-Discovery)</Title>
          <Path>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Documents\stm32f4discovery_sch.zip</Path>
        </Book>
        <Book>
          <Number>7</Number>
          <Title>MCBSTM32F400 Evaluation Board Web Page (MCBSTM32F400)</Title>
          <Path>http://www.keil.com/mcbstm32f400/</Path>
        </Book>
        <Book>
          <Number>8</Number>
          <Title>STM32F4-Discovery Web Page (STM32F4-Discovery)</Title>
          <Path>http://www.st.com/web/catalog/tools/FM116/SC959/SS1532/LN1199/PF252419</Path>
        </Book>
      </Books>
      <DllOpt>
        <SimDllName>SARMCM3.DLL</SimDllName>
        <SimDllArguments>-REMAP -MPU</SimDllArguments>
        <SimDlgDllName>DCM.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
        <TargetDllName>SARMCM3.DLL</TargetDllName>
        <TargetDllArguments>-MPU</TargetDllArguments>
        <TargetDlgDllName>TCM.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>1</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>BIN\UL2CM3.DLL</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=-1,-1,-1,-1,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(1012=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name>(105=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>-UV0998FBE -O207 -S8 -C0 -P00 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO18 -TC168000000 -TP21 -TDS808F -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -FO15 -********** -FC1000 -FN1 -FF0STM32F4xx_1024.FLM -********** -********* -FP0($$Device:STM32F407IG$Flash\STM32F4xx_1024.FLM)</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>Source</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>.\VirtualCOM.c</PathWithFileName>
      <FilenameWithoutPath>VirtualCOM.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>30</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>.\USBD_User_CDC_0.c</PathWithFileName>
      <FilenameWithoutPath>USBD_User_CDC_0.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>13</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>.\USBD_User_CDC_1.c</PathWithFileName>
      <FilenameWithoutPath>USBD_User_CDC_1.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Documentation</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>1</TopLine>
      <CurrentLine>1</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>.\Abstract.txt</PathWithFileName>
      <FilenameWithoutPath>Abstract.txt</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>::Board Support</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>1</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\Boards\Keil\MCBSTM32F400\Common\GLCD_16bitIF_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>GLCD_16bitIF_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>::CMSIS</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>1</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\CMSIS\RTX_Conf_CM.c</PathWithFileName>
      <FilenameWithoutPath>RTX_Conf_CM.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>4</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\ARM\CMSIS\3.20.4\CMSIS_RTX\Lib\ARM\RTX_CM4.lib</PathWithFileName>
      <FilenameWithoutPath>RTX_CM4.lib</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>::Device</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>1</RteFlg>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\Device\STM32F407IG\RTE_Device.h</PathWithFileName>
      <FilenameWithoutPath>RTE_Device.h</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\Device\STM32F407IG\system_stm32f4xx.c</PathWithFileName>
      <FilenameWithoutPath>system_stm32f4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\Device\STM32F407IG\startup_stm32f40_41xxx.s</PathWithFileName>
      <FilenameWithoutPath>startup_stm32f40_41xxx.s</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\DMA_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>DMA_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\GPIO_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>GPIO_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>::Drivers</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>1</RteFlg>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\OTG_FS_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>OTG_FS_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\OTG_HS_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>OTG_HS_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\UART_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>UART_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\USBD_FS_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>USBD_FS_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\STM32F4xx_DFP\1.0.7\RTE_Driver\USBD_HS_STM32F4xx.c</PathWithFileName>
      <FilenameWithoutPath>USBD_HS_STM32F4xx.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>::USB</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>1</RteFlg>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\USB\USBD_Config_0.c</PathWithFileName>
      <FilenameWithoutPath>USBD_Config_0.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\USB\USBD_Config_1.c</PathWithFileName>
      <FilenameWithoutPath>USBD_Config_1.c</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\USB\USBD_Config_CDC_0.h</PathWithFileName>
      <FilenameWithoutPath>USBD_Config_CDC_0.h</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>RTE\USB\USBD_Config_CDC_1.h</PathWithFileName>
      <FilenameWithoutPath>USBD_Config_CDC_1.h</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>4</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>C:\MDK510_ORG\ARM\PACK\Keil\MDK-Middleware\5.1.5\USB\Lib\ARM\USB_CM3_L.lib</PathWithFileName>
      <FilenameWithoutPath>USB_CM3_L.lib</FilenameWithoutPath>
      <RteFlg>1</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
