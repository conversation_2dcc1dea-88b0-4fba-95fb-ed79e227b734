;
; Keil - An ARM Company
; Communication Device Class driver installation file
;

[Version]
Signature="$Windows NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%Keil%
DriverVer=11/22/2011,*******
CatalogFile.NTx86=mcbstm32f4xx-vcom_x86.cat
CatalogFile.NTAMD64=mcbstm32f4xx-vcom_amd64.cat

[Manufacturer]
%Keil%=DeviceList,ntamd64

[DeviceList]
%DESCRIPTION%=MCBSTM32F400USB, USB\VID_c251&PID_3505
%DESCRIPTION%=MCBSTM32F400USB, USB\VID_c251&PID_3515
%COMPOSITE0% =MCBSTM32F400USB, USB\VID_c251&PID_3505&MI_00
%COMPOSITE0% =MCBSTM32F400USB, USB\VID_c251&PID_3515&MI_00
%COMPOSITE2% =MCBSTM32F400USB, USB\VID_c251&PID_3505&MI_02
%COMPOSITE2% =MCBSTM32F400USB, USB\VID_c251&PID_3515&MI_02

[DeviceList.ntamd64]
%DESCRIPTION%=MCBSTM32F400USB, USB\VID_c251&PID_3505
%DESCRIPTION%=MCBSTM32F400USB, USB\VID_c251&PID_3515
%COMPOSITE0% =MCBSTM32F400USB, USB\VID_c251&PID_3505&MI_00
%COMPOSITE0% =MCBSTM32F400USB, USB\VID_c251&PID_3515&MI_00
%COMPOSITE2% =MCBSTM32F400USB, USB\VID_c251&PID_3505&MI_02
%COMPOSITE2% =MCBSTM32F400USB, USB\VID_c251&PID_3515&MI_02

;------------------------------------------------------------------------------
;  Installation
;------------------------------------------------------------------------------

[SourceDisksFiles]

[SourceDisksNames]

[DestinationDirs]
FakeModemCopyFileSection=12
DefaultDestDir=12

[MCBSTM32F400USB]
include=mdmcpq.inf
CopyFiles=FakeModemCopyFileSection
AddReg=MCBSTM32F400USB.AddReg

[MCBSTM32F400USB.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,usbser.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[MCBSTM32F400USB.Services]
AddService=usbser, 0x00000002, DriverService

[DriverService]
DisplayName=%DRIVER.SVC%
ServiceType=1
StartType=3
ErrorControl=1
ServiceBinary=%12%\usbser.sys

;------------------------------------------------------------------------------
;  String Definitions
;------------------------------------------------------------------------------

[Strings]
Keil       = "Keil - An ARM Company"
DRIVER.SVC = "MCBSTM32F400 USB VCOM Driver"
DESCRIPTION= "MCBSTM32F400 USB VCOM Port"
COMPOSITE0 = "MCBSTM32F400 USB VCOM Port"
COMPOSITE2 = "MCBSTM32F400 USB VCOM Port"
