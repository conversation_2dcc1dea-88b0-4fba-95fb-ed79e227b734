This is an Keyboard example that demonstrates Human Interface Device (HID)
on USB Host.

Detailed description is available on:
www.keil.com/pack/doc/MW/USB/html/host_hid_tutorial.html

Board:                  Keil 'MCBSTM32F400'
Microcontroller:        ST   'STM32F407'
Clock Settings:         XTAL       =  25 MHz
                        CPUCLK     = 168 MHz
                        USB FS CLK =  48 MHz
                        USB HS CLK =  60 MHz (from external ULPI)
User Interface:         input:  USB Keyboard
                        output: Graphic LCD

The example demonstrates the use of USB Keyboard as terminal input and 
Graphic LCD as terminal output.

The example is described in detail in the Getting Started
User's Guide.

The program is available for target(s):

  - STM32F407 Flash: Downloads to and executes from internal Flash
