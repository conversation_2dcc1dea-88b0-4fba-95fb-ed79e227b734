/*------------------------------------------------------------------------------
 * MDK Middleware - Component ::USB:Host
 * Copyright (c) 2004-2013 ARM Germany GmbH. All rights reserved.
 *------------------------------------------------------------------------------
 * Name:    USBH_Config_HID.h
 * Purpose: USB Host Human Interface Device class (HID) Configuration
 * Rev.:    V5.04
 *----------------------------------------------------------------------------*/

//-------- <<< Use Configuration Wizard in Context Menu >>> --------------------

// <h>USB Host: Human Interface Device class (HID)
//   <o> Number of concurrent HID Devices in system <0-15>
#define USBH_HID_NUM                    1

// </h>
