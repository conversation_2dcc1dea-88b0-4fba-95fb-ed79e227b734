This is an USB Host and File System file manipulation example.
You can create, read, copy, delete files from the Mass Storage 
Device and format the device.

Detailed description is available on:
www.keil.com/pack/doc/MW/USB/html/host_msc_tutorial.html

Board:                  Keil 'MCBSTM32F400'
Microcontroller:        ST   'STM32F407'
Clock Settings:         XTAL       =  25 MHz
                        CPUCLK     = 168 MHz
                        USB FS CLK =  48 MHz
                        USB HS CLK =  60 MHz (from external ULPI)
User Interface:         input/output: SWO

The program is available for target(s):

  - STM32F407 Flash: Downloads to and executes from internal Flash

Documentation on the Web: 
  http://www.keil.com/support/man/docs/rlarm/rlarm_fs_ex_msd.htm
