
/*
 * Auto generated Run-Time-Environment Component Configuration File
 *      *** Do not modify ! ***
 *
 * Project: 'MassStorage' 
 * Target:  'STM32F407 Flash' 
 */

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

#define RTE_DEVICE_STARTUP_STM32F4xx    /* Device Startup for STM32F4 */
#define RTE_Drivers_USBH0               /* Driver USBH0 */
#define RTE_Drivers_USBH1               /* Driver USBH1 */
#define RTE_FileSystem_Core             /* File System Core */
          #define RTE_FileSystem_LFN              /* File System with Long Filename support */
#define RTE_FileSystem_Drive_USB_0      /* File System USB Drive 0 */
#define RTE_FileSystem_Drive_USB_1      /* File System USB Drive 1 */
#define RTE_USB_Core                    /* USB Core */
#define RTE_USB_Host_0                  /* USB Host 0 */
#define RTE_USB_Host_1                  /* USB Host 1 */
#define RTE_USB_Host_MSC                /* USB Host MSC */

#endif /* RTE_COMPONENTS_H */
