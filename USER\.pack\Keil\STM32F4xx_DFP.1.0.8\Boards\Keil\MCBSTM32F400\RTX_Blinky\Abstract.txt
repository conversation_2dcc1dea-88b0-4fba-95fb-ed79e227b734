The 'RTX_Blinky' project is a simple CMSIS RTOS Kernel based example for
ST 'STM32F407' microcontroller using Keil 'MCBSTM32F400' Evaluation Board.
Compliant to Cortex Microcontroller Software Interface Standard (CMSIS V2.0).

Example functionality:
 - Clock Settings:
   - XTAL    =  25 MHz
   - SYSCLK  = 168 MHz
   - HCLK    = 168 MHz

The simple RTX Kernel based example simulates the step-motor 
driver. Four LEDs are blinking simulating the activation of 
the four output driver stages:


- phase A
- phase B
- phase C
- phase D

This example simulates Half step driver mode and
CW rotation direction.


The BLINKY example program is available for several targets:

  STM32F407 Flash:  runs from Internal Flash located on chip
                    (used for production or target debugging)

  STM32F407 RAM:    runs from Internal RAM located on chip
                    (may be used for target debugging)
