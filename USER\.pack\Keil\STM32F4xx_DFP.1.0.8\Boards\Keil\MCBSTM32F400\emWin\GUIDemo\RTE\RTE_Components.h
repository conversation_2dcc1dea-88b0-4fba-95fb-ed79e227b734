
/*
 * Auto generated Run-Time-Environment Component Configuration File
 *      *** Do not modify ! ***
 *
 * Project: 'GUIDemo' 
 * Target:  'MCBSTM32F400' 
 */

#ifndef RTE_COMPONENTS_H
#define RTE_COMPONENTS_H

#define RTE_DEVICE_STARTUP_STM32F4xx    /* Device Startup for STM32F4 */
#define RTE_Drivers_I2C1                /* Driver I2C1 */
        #define RTE_Drivers_I2C2                /* Driver I2C2 */
        #define RTE_Drivers_I2C3                /* Driver I2C3 */
#define RTE_Graphics_Core               /* Graphics Core */
#define RTE_Graphics_Demo_AA_Text               /* Graphics Demo: AntialiasedText */
#define RTE_Graphics_Demo_Automotive            /* Graphics Demo: Automotive */
#define RTE_Graphics_Demo_BarGraph              /* Graphics Demo: BarGraph */
#define RTE_Graphics_Demo_Bitmap                /* Graphics Demo: Bitmap */
#define RTE_Graphics_Demo_ColorBar              /* Graphics Demo: ColorBar */
#define RTE_Graphics_Demo_Cursor                /* Graphics Demo: Cursor */
#define RTE_Graphics_Demo_Fading                /* Graphics Demo: Fading */
#define RTE_Graphics_Demo_Framework             /* Graphics Demo: Framework */
#define RTE_Graphics_Demo_Graph                 /* Graphics Demo: Graph */
#define RTE_Graphics_Demo_IconView              /* Graphics Demo: IconView */
#define RTE_Graphics_Demo_ListView              /* Graphics Demo: ListView */
#define RTE_Graphics_Demo_RadialMenu            /* Graphics Demo: RadialMenu */
#define RTE_Graphics_Demo_Skinning              /* Graphics Demo: Skinning */
#define RTE_Graphics_Demo_Speed                 /* Graphics Demo: Speed */
#define RTE_Graphics_Demo_Speedometer           /* Graphics Demo: Speedometer */
#define RTE_Graphics_Demo_TransparentDialog     /* Graphics Demo: TransparentDialog */
#define RTE_Graphics_Demo_Treeview              /* Graphics Demo: Treeview */
#define RTE_Graphics_Demo_WashingMachine        /* Graphics Demo: WashingMachine */
#define RTE_Graphics_Joystick           /* Graphics Input Device Joystick */
#define RTE_Graphics_LCD_MCBQVGA_LG_16  /* Graphics LCD MCBQVGA_LG 16-bit IF */
#define RTE_Graphics_Touchscreen        /* Graphics Input Device Touchscreen */

#endif /* RTE_COMPONENTS_H */
