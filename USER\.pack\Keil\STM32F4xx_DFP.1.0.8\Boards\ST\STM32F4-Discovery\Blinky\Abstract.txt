The 'Blinky' project is a simple demo program for
ST 'STM32F407VG' microcontroller using ST 'STM32F4-Discovery' Evaluation Board.
Compliant to Cortex Microcontroller Software Interface Standard (CMSIS v2.0).

Example functionality:                                                   
 - Clock Settings:
   - XTAL    =             8.00 MHz
   - SYSCLK  =           168.00 MHz
   - HCLK    = SYSCLK  = 168.00 MHz

 - 4 LEDs blink with a fixed speed, determined by SysTick Timer
     working in interrupt mode
 - if button USER is pressed all LEDs are lit.



The Blinky program is available in different targets:

  STM32F407 RAM:      configured for on-chip RAM
                      (used for target debugging)

  STM32F407 Flash:    configured for on-chip Flash
                      (used for production or target debugging)

  STM32F407 OPT:      STM32F407 with Flash Options Bytes
                      (used for programming)
