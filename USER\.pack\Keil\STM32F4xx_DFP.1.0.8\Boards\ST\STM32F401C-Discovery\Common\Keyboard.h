/*-----------------------------------------------------------------------------
 * Name:    Keyboard.h
 * Purpose: Keyboard definitions
 *-----------------------------------------------------------------------------
 * This file is part of the uVision/ARM development tools.
 * This software may only be used under the terms of a valid, current,
 * end user licence from KEIL for a compatible version of KEIL software
 * development tools. Nothing else gives you the right to use this software.
 *
 * This software is supplied "AS IS" without warranties of any kind.
 *
 * Copyright (c) 2004-2013 KEIL - An ARM Company. All rights reserved.
 *----------------------------------------------------------------------------*/

#ifndef __KEYBOARD_H
#define __KEYBOARD_H

#include <stdint.h>

extern void     Keyboard_Initialize   (void);
extern void     Keyboard_Uninitialize (void);
extern uint32_t Keyboard_GetKeys      (void);
extern uint32_t Keyboard_NumKeys      (void);

#endif /* __KEYBOARD_H */
