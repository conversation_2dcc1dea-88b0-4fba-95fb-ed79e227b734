The 'RTX_Blinky' project is a simple CMSIS RTOS Kernel based example
ST 'STM32F401VC' microcontroller using ST 'STM32F401C-Discovery' Evaluation Board.
Compliant to Cortex Microcontroller Software Interface Standard (CMSIS v2.0).

Example functionality:                                                   
 - Clock Settings:
   - XTAL    =             8.00 MHz
   - SYSCLK  =            84.00 MHz
   - HCLK    = SYSCLK  =  84.00 MHz

The simple RTX Kernel based example uses two RTX tasks
to blink a LED.


The Blinky program is available in different targets:

  STM32F401 Flash:    configured for on-chip Flash
                      (used for production or target debugging)

  STM32F401 RAM:      configured for on-chip RAM
                      (used for target debugging)
