/*----------------------------------------------------------------------------
 * Name:    Dbg_RAM.ini
 * Purpose: RAM Debug Initialization File
 * Note(s):
 *----------------------------------------------------------------------------
 * This file is part of the uVision/ARM development tools.
 * This software may only be used under the terms of a valid, current,
 * end user licence from KEIL for a compatible version of KEIL software
 * development tools. Nothing else gives you the right to use this software.
 *
 * This software is supplied "AS IS" without warranties of any kind.
 *
 * Copyright (c) 2011 Keil - An ARM Company. All rights reserved.
 *----------------------------------------------------------------------------*/

/*----------------------------------------------------------------------------
  Setup()  configure PC & SP for RAM Debug
 *----------------------------------------------------------------------------*/
FUNC void Setup (void) {
  SP = _RDWORD(0x20000000);          // Setup Stack Pointer
  PC = _RDWORD(0x20000004);          // Setup Program Counter
//  _WDWORD(0xE000ED08, 0x20000000);   // Setup Vector Table Offset Register (is done in system_stm32F4xx.c!)
}


load %L incremental

Setup();                             // Setup for Running

g, main
