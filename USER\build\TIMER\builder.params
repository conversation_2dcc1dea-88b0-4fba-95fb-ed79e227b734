{"name": "TIMER", "target": "TIMER", "toolchain": "AC5", "toolchainLocation": "d:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.11\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 16, "rootDir": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "dumpPath": "build\\TIMER", "outDir": "build\\TIMER", "ram": 196608, "rom": 1048576, "incDirs": ["../CORE", "../SYSTEM/delay", "../SYSTEM/sys", ".", "../HARDWARE/LED", "../HARDWARE/TIMER", "../FWLIB/inc", "../HARDWARE/control", "../HARDWARE/encoder", "../HARDWARE/MOTOR", "../HARDWARE/OLED", "../HARDWARE/Serial", "../HARDWARE/PWM", "../HARDWARE/PID", "../HARDWARE/MENU", "../HARDWARE/KEY", "../HARDWARE/IIC", "../HARDWARE/MPU6050", "../HARDWARE/MPU6050/eMPL", "../SYSTEM/usart", "../HARDWARE/HC_SR04", "../HARDWARE/tcs34725", "../HARDWARE/TCRT5000", "../HARDWARE/WDD35D4", ".cmsis/include", "RTE/_TIMER"], "libDirs": [], "defines": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER"], "sourceList": ["../CORE/startup_stm32f40_41xxx.s", "../FWLIB/src/misc.c", "../FWLIB/src/stm32f4xx_adc.c", "../FWLIB/src/stm32f4xx_gpio.c", "../FWLIB/src/stm32f4xx_rcc.c", "../FWLIB/src/stm32f4xx_syscfg.c", "../FWLIB/src/stm32f4xx_tim.c", "../FWLIB/src/stm32f4xx_usart.c", "../HARDWARE/HC_SR04/hc_sr04.c", "../HARDWARE/IIC/myiic.c", "../HARDWARE/KEY/key.c", "../HARDWARE/LED/led.c", "../HARDWARE/MENU/menu.c", "../HARDWARE/MOTOR/motor.c", "../HARDWARE/MPU6050/eMPL/inv_mpu.c", "../HARDWARE/MPU6050/eMPL/inv_mpu_dmp_motion_driver.c", "../HARDWARE/MPU6050/mpu6050.c", "../HARDWARE/OLED/OLED.c", "../HARDWARE/PID/pid.c", "../HARDWARE/PWM/pwm.c", "../HARDWARE/Serial/Serial.c", "../HARDWARE/TCRT5000/TCRT5000.c", "../HARDWARE/TIMER/timer.c", "../HARDWARE/WDD35D4/WDD35D4.c", "../HARDWARE/control/control.c", "../HARDWARE/encoder/encoder.c", "../HARDWARE/tcs34725/tcs34725.c", "../SYSTEM/delay/delay.c", "../SYSTEM/sys/sys.c", "../SYSTEM/usart/usart.c", "main.c", "stm32f4xx_it.c", "system_stm32f4xx.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000", "link-scatter": ["\"c:/Users/<USER>/Desktop/实验室 - 副本/USER/build/TIMER/TIMER.sct\""]}}, "env": {"KEIL_OUTPUT_DIR": "..\\OBJ", "workspaceFolder": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "workspaceFolderBasename": "USER", "OutDir": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER\\build\\TIMER", "OutDirRoot": "build", "OutDirBase": "build\\TIMER", "ProjectName": "TIMER", "ConfigName": "TIMER", "ProjectRoot": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "ExecutableName": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER\\build\\TIMER\\TIMER", "ChipPackDir": ".pack/Keil/STM32F4xx_DFP.1.0.8", "ChipName": "STM32F407ZG", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.11\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.0", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "ToolchainRoot": "d:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}