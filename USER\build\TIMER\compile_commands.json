[{"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\CORE\\startup_stm32f40_41xxx.s", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER --cpu Cortex-M4.fp --li --pd \"__MICROLIB SETA 1\" -g -o .\\build\\TIMER\\.obj\\__\\CORE\\startup_stm32f40_41xxx.o --depend .\\build\\TIMER\\.obj\\__\\CORE\\startup_stm32f40_41xxx.d .\\..\\CORE\\startup_stm32f40_41xxx.s"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\misc.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\misc.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\misc.d .\\..\\FWLIB\\src\\misc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\stm32f4xx_adc.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_adc.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_adc.d .\\..\\FWLIB\\src\\stm32f4xx_adc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\stm32f4xx_gpio.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_gpio.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_gpio.d .\\..\\FWLIB\\src\\stm32f4xx_gpio.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\stm32f4xx_rcc.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_rcc.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_rcc.d .\\..\\FWLIB\\src\\stm32f4xx_rcc.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\stm32f4xx_syscfg.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_syscfg.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_syscfg.d .\\..\\FWLIB\\src\\stm32f4xx_syscfg.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\stm32f4xx_tim.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_tim.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_tim.d .\\..\\FWLIB\\src\\stm32f4xx_tim.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\FWLIB\\src\\stm32f4xx_usart.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_usart.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\FWLIB\\src\\stm32f4xx_usart.d .\\..\\FWLIB\\src\\stm32f4xx_usart.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\HC_SR04\\hc_sr04.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\HC_SR04\\hc_sr04.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\HC_SR04\\hc_sr04.d .\\..\\HARDWARE\\HC_SR04\\hc_sr04.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\IIC\\myiic.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\IIC\\myiic.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\IIC\\myiic.d .\\..\\HARDWARE\\IIC\\myiic.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\KEY\\key.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\KEY\\key.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\KEY\\key.d .\\..\\HARDWARE\\KEY\\key.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\LED\\led.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\LED\\led.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\LED\\led.d .\\..\\HARDWARE\\LED\\led.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\MENU\\menu.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\MENU\\menu.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\MENU\\menu.d .\\..\\HARDWARE\\MENU\\menu.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\MOTOR\\motor.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\MOTOR\\motor.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\MOTOR\\motor.d .\\..\\HARDWARE\\MOTOR\\motor.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\MPU6050\\eMPL\\inv_mpu.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\MPU6050\\eMPL\\inv_mpu.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\MPU6050\\eMPL\\inv_mpu.d .\\..\\HARDWARE\\MPU6050\\eMPL\\inv_mpu.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\MPU6050\\eMPL\\inv_mpu_dmp_motion_driver.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\MPU6050\\eMPL\\inv_mpu_dmp_motion_driver.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\MPU6050\\eMPL\\inv_mpu_dmp_motion_driver.d .\\..\\HARDWARE\\MPU6050\\eMPL\\inv_mpu_dmp_motion_driver.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\MPU6050\\mpu6050.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\MPU6050\\mpu6050.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\MPU6050\\mpu6050.d .\\..\\HARDWARE\\MPU6050\\mpu6050.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\OLED\\OLED.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\OLED\\OLED.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\OLED\\OLED.d .\\..\\HARDWARE\\OLED\\OLED.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\PID\\pid.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\PID\\pid.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\PID\\pid.d .\\..\\HARDWARE\\PID\\pid.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\PWM\\pwm.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\PWM\\pwm.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\PWM\\pwm.d .\\..\\HARDWARE\\PWM\\pwm.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\Serial\\Serial.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\Serial\\Serial.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\Serial\\Serial.d .\\..\\HARDWARE\\Serial\\Serial.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\TCRT5000\\TCRT5000.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\TCRT5000\\TCRT5000.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\TCRT5000\\TCRT5000.d .\\..\\HARDWARE\\TCRT5000\\TCRT5000.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\TIMER\\timer.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\TIMER\\timer.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\TIMER\\timer.d .\\..\\HARDWARE\\TIMER\\timer.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\WDD35D4\\WDD35D4.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\WDD35D4\\WDD35D4.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\WDD35D4\\WDD35D4.d .\\..\\HARDWARE\\WDD35D4\\WDD35D4.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\control\\control.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\control\\control.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\control\\control.d .\\..\\HARDWARE\\control\\control.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\encoder\\encoder.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\encoder\\encoder.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\encoder\\encoder.d .\\..\\HARDWARE\\encoder\\encoder.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\HARDWARE\\tcs34725\\tcs34725.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\HARDWARE\\tcs34725\\tcs34725.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\HARDWARE\\tcs34725\\tcs34725.d .\\..\\HARDWARE\\tcs34725\\tcs34725.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\SYSTEM\\delay\\delay.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\SYSTEM\\delay\\delay.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\SYSTEM\\delay\\delay.d .\\..\\SYSTEM\\delay\\delay.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\SYSTEM\\sys\\sys.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\SYSTEM\\sys\\sys.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\SYSTEM\\sys\\sys.d .\\..\\SYSTEM\\sys\\sys.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\SYSTEM\\usart\\usart.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\__\\SYSTEM\\usart\\usart.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\__\\SYSTEM\\usart\\usart.d .\\..\\SYSTEM\\usart\\usart.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER\\main.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\main.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\main.d .\\main.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER\\stm32f4xx_it.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\stm32f4xx_it.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\stm32f4xx_it.d .\\stm32f4xx_it.c"}, {"directory": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER", "file": "c:\\Users\\<USER>\\Desktop\\实验室 - 副本\\USER\\system_stm32f4xx.c", "command": "\"d:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../CORE -I../SYSTEM/delay -I../SYSTEM/sys -I. -I../HARDWARE/LED -I../HARDWARE/TIMER -I../FWLIB/inc -I../HARDWARE/control -I../HARDWARE/encoder -I../HARDWARE/MOTOR -I../HARDWARE/OLED -I../HARDWARE/Serial -I../HARDWARE/PWM -I../HARDWARE/PID -I../HARDWARE/MENU -I../HARDWARE/KEY -I../HARDWARE/IIC -I../HARDWARE/MPU6050 -I../HARDWARE/MPU6050/eMPL -I../SYSTEM/usart -I../HARDWARE/HC_SR04 -I../HARDWARE/tcs34725 -I../HARDWARE/TCRT5000 -I../HARDWARE/WDD35D4 -I.cmsis/include -IRTE/_TIMER -D\"STM32F40_41xxx\" -D\"USE_STDPERIPH_DRIVER\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\TIMER\\.obj\\system_stm32f4xx.o --no_depend_system_headers --depend .\\build\\TIMER\\.obj\\system_stm32f4xx.d .\\system_stm32f4xx.c"}]